/* Tooltip 组件样式 */

.tooltip-container {
  position: relative;
  display: inline-block;
}

.tooltip-content {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(229, 231, 235, 0.8);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  width: 224px; /* w-56 */
  max-width: 224px;
  word-wrap: break-word;
  word-break: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  white-space: normal;
  box-sizing: border-box;
}

.tooltip-enter {
  opacity: 0;
  transform: scale(0.95) translateY(-2px);
}

.tooltip-enter-active {
  opacity: 1;
  transform: scale(1) translateY(0);
  transition: opacity 200ms cubic-bezier(0.4, 0, 0.2, 1), 
              transform 200ms cubic-bezier(0.4, 0, 0.2, 1);
}

.tooltip-exit {
  opacity: 1;
  transform: scale(1) translateY(0);
}

.tooltip-exit-active {
  opacity: 0;
  transform: scale(0.95) translateY(-2px);
  transition: opacity 150ms cubic-bezier(0.4, 0, 0.2, 1), 
              transform 150ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* 箭头样式优化 */
.tooltip-arrow {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* 响应式设计 */
@media (max-width: 640px) {
  .tooltip-content {
    width: 200px;
    max-width: 200px;
    font-size: 0.75rem;
    padding: 0.5rem 0.75rem;
  }
}

@media (max-width: 480px) {
  .tooltip-content {
    width: 180px;
    max-width: 180px;
    font-size: 0.7rem;
    padding: 0.5rem 0.625rem;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .tooltip-content {
    border-width: 2px;
    border-color: #000;
    background-color: #fff;
    color: #000;
    backdrop-filter: none;
    -webkit-backdrop-filter: none;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .tooltip-enter-active,
  .tooltip-exit-active {
    transition: opacity 100ms ease-in-out;
    transform: none;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .tooltip-content {
    background: rgba(31, 41, 55, 0.95);
    border-color: rgba(75, 85, 99, 0.8);
    color: #f9fafb;
  }
}
