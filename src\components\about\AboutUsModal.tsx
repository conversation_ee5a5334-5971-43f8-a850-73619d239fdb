import React from 'react';
import { Modal } from 'antd';
import { useSimpleTranslation } from '../../i18n/simple-hooks';
import { IMAGE_URLS } from '../icons/Icons';

interface AboutUsModalProps {
  open: boolean;
  onClose: () => void;
}

const AboutUsModal: React.FC<AboutUsModalProps> = ({ open, onClose }) => {
  const { t } = useSimpleTranslation();

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      centered
      width={500}
      zIndex={5000}
      title={t('footer.aboutUs')}
    >
      <div className="py-4">
        {/* 公司简介 */}
        <div className="mb-4">
          <h3 className="text-base font-medium mb-2">{t('about.companyIntro')}</h3>
          <p className="text-sm text-gray-600 leading-relaxed">
            {t('about.companyDescription')}
          </p>
        </div>

        {/* 联系方式 */}
        <div className="mb-4">
          <h3 className="text-base font-medium mb-2">{t('about.contactInfo')}</h3>
          <div className="text-sm text-gray-600">
            <p className="mb-1">
              <span className="font-medium">{t('about.email')}：</span>
              <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
                <EMAIL>
              </a>
            </p>
            <p className="mb-1">
              <span className="font-medium">{t('about.website')}：</span>
              <a href="https://www.medsci.cn" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                www.medsci.cn
              </a>
            </p>
          </div>
        </div>

        {/* 客服二维码 */}
        <div>
          <h3 className="text-base font-medium mb-2">{t('about.customerService')}</h3>
          <div className="flex justify-center">
            <img 
              src={IMAGE_URLS.CUSTOMER_SERVICE_QR}
              alt={t('about.scanQRCode')} 
              className="w-40 h-40 object-cover"
            />
          </div>
          <p className="text-xs text-center text-gray-500 mt-2">
            {t('about.scanQRCode')}
          </p>
        </div>
      </div>
    </Modal>
  );
};

export default AboutUsModal;
